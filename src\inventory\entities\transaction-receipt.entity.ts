import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TransactionDetail } from './transaction-detail.entity';
import { TransactionType } from '../common/inventory.contants';
import { Staff } from 'src/staff/staff.entity';

@Entity('TransactionReceipt')
export class TransactionReceipt {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  transactionDate: Date;

  @Column({ type: 'enum', enum: TransactionType })
  transactionType: TransactionType;

  @Column()
  staffId: number;

  @OneToOne(() => Staff)
  @JoinColumn()
  staff: Staff;

  @Column()
  note: string;

  @OneToMany(() => TransactionDetail, (detail) => detail.transactionReceipt)
  @JoinColumn()
  details: TransactionDetail[];
}
