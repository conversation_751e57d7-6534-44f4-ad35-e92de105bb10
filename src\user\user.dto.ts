import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class ChangeSettingDto {
  @IsNotEmpty()
  @IsNumber()
  minPrice: number;

  @IsNotEmpty()
  @IsNumber()
  convertCusReq: number;

  @IsNumber({}, { each: true })
  applyRuleStaffType: number[];

  @IsNumber()
  applyStart?: number;

  @IsNumber()
  applyEnd?: number;
}

export class SettingInfoDto {
  @IsOptional()
  @IsNumber()
  dateOffset?: number;
}

export class AddNoteDto {
  @IsString()
  content: string;
}

export class GetNoteDto {
  @IsNotEmpty()
  @IsNumber()
  dateOffset: number;
}
