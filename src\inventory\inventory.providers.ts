import { DataSource } from 'typeorm';
import { TransactionReceipt } from './entities/transaction-receipt.entity';
import { TransactionDetail } from './entities/transaction-detail.entity';
import { Product } from './entities/product.entity';
import { constants } from './common/inventory.contants';
import { constants as commonConstants } from 'src/constants';
import { ProductType } from './entities/product-type.entity';
import { InventoryNote } from './entities/inventory-note.entity';

export const inventoryProviders = [
  {
    provide: constants.db.TRANSACTION_RECEIPT_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(TransactionReceipt),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.TRANSACTION_DETAIL_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(TransactionDetail),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.PRODUCT_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Product),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.PRODUCT_TYPE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(ProductType),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.INVENTORY_NOTE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(InventoryNote),
    inject: [commonConstants.db.DATA_SOURCE],
  },
];
