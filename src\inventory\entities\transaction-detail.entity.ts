import { Column, <PERSON>tity, <PERSON>inC<PERSON>umn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Product } from './product.entity';
import { TransactionReceipt } from './transaction-receipt.entity';

@Entity('TransactionDetail')
export class TransactionDetail {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  transactionReceiptId: number;

  @Column()
  productId: number;

  @OneToOne(() => Product)
  @JoinColumn()
  product: Product;

  @Column()
  quantity: number;

  @Column()
  note: string;

  @ManyToOne(() => TransactionReceipt, (transactionReceipt) => transactionReceipt.details)
  @JoinColumn({ name: 'transactionReceiptId', referencedColumnName: 'id' })
  transactionReceipt: TransactionReceipt;
}
