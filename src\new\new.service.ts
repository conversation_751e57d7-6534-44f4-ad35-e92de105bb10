import { Inject } from '@nestjs/common';
import { Repository } from 'typeorm';
import { constants as commonConstants } from 'src/constants';
import { StaffWork } from 'src/staff/staff-work.entity';
import { subDays } from 'date-fns';
import { Staff } from 'src/staff/staff.entity';
import { endOfDay, startOfDay } from 'src/utils';
import { StaffType } from 'src/staff/staff-type.entity';

export class NewService {
  constructor(
    @Inject(commonConstants.db.STAFF_WORK_REPOSITORY)
    private staffWorkRepository: Repository<StaffWork>,

    @Inject(commonConstants.db.STAFF_REPOSITORY)
    private staffRepository: Repository<Staff>,

    @Inject(commonConstants.db.STAFF_TYPE_REPOSITORY)
    private staffTypeRepository: Repository<StaffType>,
  ) {}

  async getAllStaffWork(fromDateOffset = 0, toDateOffset: number) {
    const _toDateOffset = isNaN(toDateOffset) ? fromDateOffset : toDateOffset;
    const _fromDate = subDays(new Date(), fromDateOffset);
    const _toDate = subDays(new Date(), _toDateOffset);
    const data = await this.staffRepository
      .createQueryBuilder('staff')
      .leftJoin(
        StaffWork,
        'work',
        '(work.staffId = staff.id) AND (work.timeStart BETWEEN :todayStart AND :todayEnd)',
        { todayStart: startOfDay(_fromDate), todayEnd: endOfDay(_toDate) },
      )
      .where('staff.isDelete = :isDelete', { isDelete: false })
      .select([
        'staff.id AS id',
        'staff.name AS name',
        'staff.staffTypeId AS staffTypeId',
        'COALESCE(SUM(work.turn), 0) AS totalTurn',
        'COALESCE(SUM(work.realTurn), 0) AS totalRealTurn',
        'COALESCE(SUM(work.specTurn), 0) AS totalSpecTurn',
        'COALESCE(SUM(work.customersReq), 0) AS totalCustomersReq',
      ])
      .groupBy('staff.id')
      .addGroupBy('staff.name')
      .orderBy('staff.name', 'ASC')
      .getRawMany();

    return data.map((row) => ({
      ...row,
      totalTurn: Number(row.totalTurn),
      totalRealTurn: Number(row.totalRealTurn),
      totalSpecTurn: Number(row.totalSpecTurn),
      totalCustomersReq: Number(row.totalCustomersReq),
      total: Number(row.totalRealTurn) + Number(row.totalCustomersReq),
    }));
  }

  async getGroupStaffWorkByStaffType(fromDateOffset = 0, toDateOffset: number) {
    const _toDateOffset = isNaN(toDateOffset) ? fromDateOffset : toDateOffset;
    const _fromDate = subDays(new Date(), fromDateOffset);
    const _toDate = subDays(new Date(), _toDateOffset);

    const data = await this.staffTypeRepository
      .createQueryBuilder('type')
      .leftJoin(Staff, 'staff', 'staff.staffTypeId = type.id')
      .leftJoin(
        StaffWork,
        'work',
        '(work.staffId = staff.id) AND (work.timeStart BETWEEN :todayStart AND :todayEnd)',
        { todayStart: startOfDay(_fromDate), todayEnd: endOfDay(_toDate) },
      )
      .select([
        'type.id AS staffTypeId',
        'type.name AS staffTypeName',
        'COALESCE(SUM(work.turn), 0) AS totalTurn',
        'COALESCE(SUM(work.realTurn), 0) AS totalRealTurn',
        'COALESCE(SUM(work.specTurn), 0) AS totalSpecTurn',
        'COALESCE(SUM(work.customersReq), 0) AS totalCustomersReq',
      ])
      .groupBy('type.id')
      .addGroupBy('type.name')
      .orderBy('type.id', 'ASC')
      .getRawMany();

    return data.map((row) => ({
      ...row,
      totalTurn: Number(row.totalTurn),
      totalRealTurn: Number(row.totalRealTurn),
      totalSpecTurn: Number(row.totalSpecTurn),
      totalCustomersReq: Number(row.totalCustomersReq),
      total: Number(row.totalRealTurn) + Number(row.totalCustomersReq),
    }));
  }

  async getGroupStaffWorkByDate(
    fromDateOffset = 0,
    toDateOffset = 0,
    groupBy: 'day' | 'week' | 'month' | 'year' = 'month',
    staffTypeId?: number,
    page = 1,
    limit = 10,
  ) {
    const startDate = startOfDay(subDays(new Date(), fromDateOffset));
    const endDate = endOfDay(subDays(new Date(), toDateOffset));

    let groupSelect = '';
    switch (groupBy) {
      case 'day':
        groupSelect = 'DATE(work.timeStart)';
        break;
      case 'week':
        groupSelect = 'YEAR(work.timeStart) * 100 + WEEK(work.timeStart, 1)';
        break;
      case 'month':
        groupSelect = 'YEAR(work.timeStart) * 100 + MONTH(work.timeStart)';
        break;
      case 'year':
        groupSelect = 'YEAR(work.timeStart)';
        break;
    }

    const baseQuery = this.staffWorkRepository
      .createQueryBuilder('work')
      .leftJoin(Staff, 'staff', 'staff.id = work.staffId')
      .leftJoin(StaffType, 'type', 'type.id = staff.staffTypeId')
      .andWhere('work.timeStart BETWEEN :startDate AND :endDate', { startDate, endDate });

    if (staffTypeId) baseQuery.andWhere('type.id = :staffTypeId', { staffTypeId });
    baseQuery
      .select(`${groupSelect}`, 'groupKey')
      .addSelect('SUM(work.turn)', 'totalTurn')
      .addSelect('SUM(work.realTurn)', 'totalRealTurn')
      .addSelect('SUM(work.specTurn)', 'totalSpecTurn')
      .addSelect('SUM(work.customersReq)', 'totalCustomersReq')
      .groupBy('groupKey')
      .orderBy('groupKey', 'DESC');

    const offset = (page - 1) * limit;
    const rows = await baseQuery.clone().offset(offset).limit(limit).getRawMany();
    const data = rows.map((row) => ({
      ...row,
      totalTurn: Number(row.totalTurn),
      totalRealTurn: Number(row.totalRealTurn),
      totalSpecTurn: Number(row.totalSpecTurn),
      totalCustomersReq: Number(row.totalCustomersReq),
      total: Number(row.totalRealTurn) + Number(row.totalCustomersReq),
    }));

    const total = (await baseQuery.clone().getRawMany()).length;

    return {
      data,
      total,
      currentPage: page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
