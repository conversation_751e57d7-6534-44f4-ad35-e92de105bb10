import { Module, forwardRef } from '@nestjs/common';
import { DatabaseModule } from 'src/database/database.module';
import { staffProviders } from './staff.providers';
import { StaffService } from './staff.service';
import { StaffController } from './staff.controller';
import { WorkModule } from 'src/work/work.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [DatabaseModule, forwardRef(() => WorkModule), UserModule],
  providers: [...staffProviders, StaffService],
  controllers: [StaffController],
  exports: [StaffService],
})
export class StaffModule {}
