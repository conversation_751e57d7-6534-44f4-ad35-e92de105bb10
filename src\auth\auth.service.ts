import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from 'src/user/user.service';
import { User } from 'src/user/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
  ) {}

  async signIn(username: string, password: string): Promise<any> {
    const user = await this.userService.findOne(username);
    if (!user) throw new UnauthorizedException();

    const isCorrectPassword = await User.comparePassword(user, password);
    if (!isCorrectPassword) throw new UnauthorizedException();

    const payload = {
      id: user.id,
      username: user.username,
    };

    return {
      accessToken: await this.jwtService.signAsync(payload),
    };
  }

  async changePassword(id: number, oldPass: string, password: string) {
    if (!id) throw new UnauthorizedException();
    const _user = await this.userService.findOneID(id);
    if (!_user) throw new UnauthorizedException();

    const _isCorrectPassword = await User.comparePassword(_user, oldPass);
    if (!_isCorrectPassword) throw new UnauthorizedException();

    const _newPass = await User.hashPassword(password);
    _user.password = _newPass;
    await this.userService.save(_user);
    return true;
  }
}
