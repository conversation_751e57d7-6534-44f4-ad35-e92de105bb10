import { InventoryController } from './inventory.controller';
import { InventoryService } from './inventory.service';
import { inventoryProviders } from './inventory.providers';
import { DatabaseModule } from 'src/database/database.module';
import { Module } from '@nestjs/common';

@Module({
  imports: [DatabaseModule],
  providers: [...inventoryProviders, InventoryService],
  controllers: [InventoryController],
  exports: [InventoryService],
})
export class InventoryModule {}
