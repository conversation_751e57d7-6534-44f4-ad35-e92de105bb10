import { Inject, Injectable } from '@nestjs/common';
import { constants } from 'src/constants';

import { Repository } from 'typeorm';
import { User } from './user.entity';
import { Transactional } from 'typeorm-transactional';
import { Setting } from './setting.entity';
import { isDateApplyRule } from 'src/utils';
import { ApplyRule } from './apply-rule.entity';
import { subDays } from 'date-fns';
import { Note } from './note.entity';

@Injectable()
export class UserService {
  constructor(
    @Inject(constants.db.USER_REPOSITORY)
    private userRepository: Repository<User>,

    @Inject(constants.db.SETTING_REPOSITORY)
    private settingRepository: Repository<Setting>,

    @Inject(constants.db.APPLY_RULE_REPOSITORY)
    private applyRuleRepo: Repository<ApplyRule>,

    @Inject(constants.db.NOTE_REPOSITORY)
    private noteRepository: Repository<Note>,
  ) {}

  async getApplyRuleStaffType() {
    const _applyRule = await this.applyRuleRepo.find();
    return _applyRule.map((el) => el.staffTypeId);
  }

  async getSetting(dateOffset = 0) {
    let _settingArr = await this.settingRepository.find();
    if (_settingArr.length < 1) {
      const _setting = new Setting();
      await this.settingRepository.save(_setting);
      _settingArr = await this.settingRepository.find();
    }

    const _setting = _settingArr[0];
    _setting.applyRuleStaffType = await this.getApplyRuleStaffType();
    _setting.isDateApplyRule = isDateApplyRule(
      subDays(new Date(), dateOffset),
      _setting.applyStart,
      _setting.applyEnd,
    );

    return _setting;
  }

  @Transactional()
  async setApplyRule(staffTypes: number[]) {
    await this.applyRuleRepo.clear();

    const _newRule: ApplyRule[] = staffTypes.map((el) => {
      const _rule = new ApplyRule();
      _rule.staffTypeId = el;
      return _rule;
    });

    await this.applyRuleRepo.save(_newRule);
  }

  @Transactional()
  async changeSetting(data: any, staffTypes: number[]) {
    const _setting = await this.getSetting();
    await this.setApplyRule(staffTypes);
    return this.settingRepository.save({ ...data, id: _setting.id });
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  async findOne(username: string): Promise<User> {
    return this.userRepository.findOne({ where: { username } });
  }

  async findOneID(id: number): Promise<User> {
    return this.userRepository.findOne({ where: { id } });
  }

  @Transactional()
  async save(data: any): Promise<User> {
    return this.userRepository.save(data);
  }

  async addNote(content: string): Promise<Note> {
    const _multiNote = await this.noteRepository.find({
      // where: { timeStart: Between(startOfDay(_date), endOfDay(_date)) },
      order: { id: 'ASC' },
    });

    if (_multiNote.length < 1) {
      return this.createNewNote(new Date(), content);
    }

    const _note = _multiNote[0];
    _note.content = content;
    return this.noteRepository.save(_note);
  }

  async getNote(dateOffset: number): Promise<Note> {
    const _date = subDays(new Date(), dateOffset);

    const _multiNote = await this.noteRepository.find({
      // where: { timeStart: Between(startOfDay(_date), endOfDay(_date)) },
      order: { id: 'ASC' },
    });

    if (_multiNote.length < 1) {
      return this.createNewNote(_date);
    }

    return _multiNote[0];
  }

  async createNewNote(date: Date, content = ''): Promise<Note> {
    const _note = new Note();
    _note.timeStart = date;
    _note.content = content;
    return this.noteRepository.save(_note);
  }
}
