import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from 'src/database/database.module';
import { taskProviders } from './task.providers';
import { TaskService } from './task.service';
import { TaskController } from './task.controller';

@Module({
  imports: [DatabaseModule],
  providers: [...taskProviders, TaskService],
  controllers: [TaskController],
  exports: [TaskService],
})
export class TaskModule {}
