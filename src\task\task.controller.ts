import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';

import { TaskService } from './task.service';
import {
  TaskCreateDto,
  TaskDeleteDto,
  TaskSortDto,
  TaskTypeCreateDto,
  TaskTypeDeleteDto,
  TaskTypeUpdateDto,
  TaskUpdateDto,
} from './task.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('task')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Get('')
  getAll() {
    return this.taskService.findAll();
  }

  @Post('create')
  create(@Body() taskDto: TaskCreateDto) {
    return this.taskService.save(taskDto);
  }

  @Post('update')
  update(@Body() taskDto: TaskUpdateDto) {
    return this.taskService.save(taskDto);
  }

  @Post('love')
  love(@Body() taskDto: TaskDeleteDto) {
    return this.taskService.love(taskDto.id);
  }

  @Post('delete')
  delete(@Body() taskDto: TaskDeleteDto) {
    return this.taskService.save({ ...taskDto, isDelete: true });
  }

  @Post('sort')
  sort(@Body() taskDto: TaskSortDto) {
    return this.taskService.sort(taskDto.orders);
  }

  @Get('type')
  getAllType() {
    return this.taskService.findAllType();
  }

  @Post('type/create')
  createType(@Body() taskTypeDto: TaskTypeCreateDto) {
    return this.taskService.saveType(taskTypeDto);
  }

  @Post('type/update')
  updateType(@Body() taskTypeDto: TaskTypeUpdateDto) {
    return this.taskService.saveType(taskTypeDto);
  }

  @Post('type/delete')
  deleteType(@Body() taskTypeDto: TaskTypeDeleteDto) {
    return this.taskService.saveType({ ...taskTypeDto, isDelete: true });
  }
}
