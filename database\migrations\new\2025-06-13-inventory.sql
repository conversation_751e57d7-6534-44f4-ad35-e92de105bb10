CREATE TABLE ProductType (
    id int NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deletedAt TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE Product (
    id int NOT NULL AUTO_INCREMENT,
    productTypeId int NOT NULL,
    name varchar(255) NOT NULL,
    unit varchar(255) NOT NULL,
    warningQuantity int NOT NULL DEFAULT 0,
    qualifiedQuantity int NOT NULL DEFAULT 0,
    quantity int NOT NULL DEFAULT 0,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deletedAt TIMESTAMP,
    PRIMARY KEY (id),
    FOR<PERSON><PERSON><PERSON> KEY (productTypeId) REFERENCES ProductType(id)
);

CREATE TABLE TransactionReceipt ( 
    id int NOT NULL AUTO_INCREMENT,
    transactionDate DATETIME NOT NULL,
    transactionType enum('IMPORT', 'EXPORT', 'REQUEST') NOT NULL,
    staffId int,
    note TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (staffId) REFERENCES Staff(id)
);

CREATE TABLE TransactionDetail (
    id int NOT NULL AUTO_INCREMENT,
    transactionReceiptId int NOT NULL,
    productId int NOT NULL,
    quantity int NOT NULL,
    note TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (transactionReceiptId) REFERENCES TransactionReceipt(id),
    FOREIGN KEY (productId) REFERENCES Product(id)
);
