import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ProductType } from './product-type.entity';

@Entity('Product')
export class Product {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  name: string;

  @Column()
  warningQuantity: number;

  @Column()
  qualifiedQuantity: number;

  @Column()
  productTypeId: number;

  @OneToOne(() => ProductType)
  @JoinColumn()
  productType: ProductType;

  @Column()
  unit: string;

  @Column()
  quantity: number;

  @Column()
  deletedAt: Date;

  @Column()
  productOrder: number;
}
