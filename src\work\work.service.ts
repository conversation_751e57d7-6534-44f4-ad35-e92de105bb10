import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { constants } from 'src/constants';

import { Between, Repository } from 'typeorm';
import { Work } from './work.entity';
import { Task } from 'src/task/task.entity';
import { StaffService, WORK_STATUS } from 'src/staff/staff.service';
import { addSeconds, differenceInSeconds, subSeconds } from 'date-fns';
import { endOfDay, startOfDay } from 'src/utils';
import { Transactional } from 'typeorm-transactional';
import { UserService } from 'src/user/user.service';

class FindOptions {
  isDelete?: boolean;
  staffId?: number;
  timeStart?: any;
}

class FindArg {
  staffId?: number;
  fromDate?: Date;
  toDate?: Date;
}

@Injectable()
export class WorkService {
  constructor(
    @Inject(constants.db.WORK_REPOSITORY)
    private workRepo: Repository<Work>,
    @Inject(forwardRef(() => StaffService))
    private staffService: StaffService,
    @Inject(UserService)
    private userService: UserService,
  ) {}

  async isSpecTurn(total: number) {
    const _setting = await this.userService.getSetting();
    return total < _setting.minPrice;
  }

  @Transactional()
  async start(
    staffId: number,
    tasks?: number[],
    customerRequest = false,
    startAfterSec = 0,
  ): Promise<Work> {
    const _work = new Work();
    _work.timeStart = addSeconds(new Date(), startAfterSec);
    _work.time = 0;
    _work.onWorking = true;
    _work.staffId = staffId;
    _work.customerRequest = customerRequest;

    if (tasks) {
      _work.tasks = [...new Set(tasks)].map((el) => {
        const _task = new Task();
        _task.id = el;
        return _task;
      });
    }

    await this.staffService.startWork(staffId, _work.timeStart);
    return this.workRepo.save(_work);
  }

  workStatus(isSpecTurn = false, isCusReq = false) {
    if (isCusReq) return WORK_STATUS.CUSTOMER_REQ;
    if (isSpecTurn) return WORK_STATUS.SPEC_TURN;
    return WORK_STATUS.TURN;
  }

  @Transactional()
  async update(
    id: number,
    staffId: number,
    tasks: number[],
    customerRequest = false,
    secOffset = 0,
  ) {
    let _work = await this.workRepo.findOne({ where: { id } });
    if (!_work) throw new BadRequestException('Work not found');

    // Save tasks firsts
    _work.tasks = [...new Set(tasks)].map((el) => {
      const _task = new Task();
      _task.id = el;
      return _task;
    });
    await this.workRepo.save(_work);

    // Get new tasks
    _work = await this.workRepo.findOne({
      relations: ['tasks'],
      where: { id },
    });

    const total = _work.tasks.reduce((total, task) => total + task.price, 0);
    const _isSpecTurn = await this.isSpecTurn(total);

    if (_work.onWorking) {
      if (_work.staffId != staffId) {
        await this.staffService.endWork(
          _work.staffId,
          _work.timeStart,
          WORK_STATUS.NOT_CHANGE,
        );
        await this.staffService.startWork(staffId, _work.timeStart);
      }
    } else {
      const _from = this.workStatus(_work.isSpecTurn, _work.customerRequest);
      const _to = this.workStatus(_isSpecTurn, customerRequest);

      await this.staffService.moveTurnToStaff(
        _work.staffId,
        staffId,
        _work.timeStart,
        _from,
        _to,
      );
    }

    if (_work.onWorking) {
      _work.timeStart = subSeconds(_work.timeStart, secOffset);
      await this.staffService.updateStartOn(_work.staffId, _work.timeStart);
    } else {
      _work.time += secOffset;
    }

    _work.customerRequest = customerRequest;
    _work.staffId = staffId;
    if (!_work.onWorking) _work.isSpecTurn = _isSpecTurn;

    return this.workRepo.save(_work);
  }

  @Transactional()
  async end(staffId: number, date = new Date()) {
    const _current = new Date(date);

    const _work = await this.workRepo.findOne({
      relations: ['tasks'],
      // relationLoadStrategy: 'query',
      where: {
        staffId,
        onWorking: true,
        isDelete: false,
        timeStart: Between(startOfDay(_current), endOfDay(_current)),
      },
    });

    let _workStatus = WORK_STATUS.NOT_CHANGE;

    if (_work) {
      const _time = differenceInSeconds(_current, _work.timeStart);
      if (_time < 0) return this.deleteWork(_work.id);

      _work.timeEnd = _current;
      _work.onWorking = false;
      _work.time += _time;

      const total = _work.tasks.reduce((total, task) => total + task.price, 0);
      _work.isSpecTurn = await this.isSpecTurn(total);

      _workStatus = this.workStatus(_work.isSpecTurn, _work.customerRequest);
      await this.workRepo.save(_work);
    }

    return this.staffService.endWork(staffId, _current, _workStatus);
  }

  @Transactional()
  async terminate(staffId: number): Promise<Work> {
    const _work = await this.workRepo.findOne({
      where: { staffId, onWorking: true, isDelete: false },
    });

    if (!_work) throw new BadRequestException('Work not found');

    _work.isDelete = true;
    _work.onWorking = false;

    return this.workRepo.save(_work);
  }

  @Transactional()
  async deleteWork(id: number): Promise<Work> {
    const _work = await this.workRepo.findOne({
      where: { id, isDelete: false },
    });

    if (!_work) throw new BadRequestException('Work not found');

    if (_work.onWorking) {
      _work.onWorking = false;
      await this.staffService.removeWorking(_work.staffId, _work.timeStart);
    } else {
      const _status = this.workStatus(_work.isSpecTurn, _work.customerRequest);
      await this.staffService.subTurn(_work.staffId, _work.timeStart, _status);
    }

    _work.isDelete = true;
    return this.workRepo.save(_work);
  }

  async findAll({ staffId, fromDate, toDate }: FindArg): Promise<Work[]> {
    const options: FindOptions = { isDelete: false };

    if (staffId) options.staffId = staffId;
    if (fromDate) {
      options.timeStart = Between(
        startOfDay(fromDate),
        endOfDay(toDate || fromDate),
      );
    }

    return this.workRepo.find({
      relations: ['tasks'],
      // relationLoadStrategy: 'query',
      where: { ...options },
    });
  }

  async findOneByStaffID(staffId: number): Promise<Work> {
    return this.workRepo.findOne({
      relations: ['tasks'],
      // relationLoadStrategy: 'query',
      where: { staffId },
      order: { timeStart: 'ASC' },
    });
  }

  async findOne(id: number): Promise<Work> {
    return this.workRepo.findOne({
      relations: ['tasks'],
      // relationLoadStrategy: 'query',
      where: { id },
    });
  }

  @Transactional()
  async skipWork(staffId: number): Promise<Work> {
    const _work = new Work();
    _work.timeStart = new Date();
    _work.timeEnd = new Date();
    _work.time = 0;
    _work.onWorking = false;
    _work.staffId = staffId;
    _work.isSkip = true;
    _work.customerRequest = false;

    await this.staffService.skipWork(staffId, _work.timeStart);
    return this.workRepo.save(_work);
  }

  @Transactional()
  async continueWork(id: number) {
    const _work = await this.workRepo.findOne({
      where: { id, isDelete: false },
    });

    if (!_work) throw new BadRequestException('Work not found');
    if (_work.onWorking) throw new BadRequestException('Work on working');

    const _status = this.workStatus(_work.isSpecTurn, _work.customerRequest);
    await this.staffService.subTurn(_work.staffId, _work.timeStart, _status);

    _work.onWorking = true;
    _work.timeStart = new Date();

    await this.staffService.startWork(_work.staffId, _work.timeStart);
    return this.workRepo.save(_work);
  }
}
