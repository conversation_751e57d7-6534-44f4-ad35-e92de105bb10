import { Module, forwardRef } from '@nestjs/common';
import { DatabaseModule } from 'src/database/database.module';
import { workProviders } from './work.providers';
import { WorkService } from './work.service';
import { WorkController } from './work.controller';
import { StaffModule } from 'src/staff/staff.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [DatabaseModule, forwardRef(() => StaffModule), UserModule],
  providers: [...workProviders, WorkService],
  controllers: [WorkController],
  exports: [WorkService],
})
export class WorkModule {}
