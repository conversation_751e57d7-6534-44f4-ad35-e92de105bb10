import { IsNotEmpty } from 'class-validator';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TaskType } from './task-type.entity';

@Entity('Task')
export class Task {
  @PrimaryGeneratedColumn('increment')
  public id: number;

  @IsNotEmpty()
  @Column()
  public name: string;

  @IsNotEmpty()
  @Column()
  public time: number;

  @IsNotEmpty()
  @Column()
  public price: number;

  @Column()
  public orderIndex: number;

  @IsNotEmpty()
  @Column()
  public taskTypeId: number;

  @OneToOne(() => TaskType)
  @JoinColumn()
  public taskType: TaskType;

  @Column()
  public isDelete: boolean;

  @Column()
  public isLove: boolean;
}
