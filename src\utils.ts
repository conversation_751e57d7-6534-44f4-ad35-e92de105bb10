import { env } from './env';

import * as moment from 'moment-timezone';

moment.tz.setDefault(env.setting.timezone);
export const sleep = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export function startOfDay(date: Date): Date {
  const current = moment(date);
  return current.startOf('day').toDate();
}

export function endOfDay(date: Date): Date {
  const current = moment(date);
  return current.endOf('day').toDate();
}

export function safeEndOfDay(date: Date): Date {
  const current = moment(date);
  return current.endOf('day').subtract(5, 'minutes').toDate();
}

export function isDateApplyRule(date: Date, start: number, end: number) {
  // Not day in week
  if (start < 1 || start > 7 || end < 1 || end > 7) return false;

  const _today = moment(new Date(date));
  const _weekday = _today.isoWeekday();

  if (start <= end) {
    for (let i = start; i <= end; i++) {
      if (_weekday == i) return true;
    }
  } else {
    for (let i = start; i <= 7; i++) {
      if (_weekday == i) return true;
    }

    for (let i = 1; i <= end; i++) {
      if (_weekday == i) return true;
    }
  }

  return false;
}
