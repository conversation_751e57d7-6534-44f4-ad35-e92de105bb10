import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { IsNotEmpty } from 'class-validator';
import { AuthGuard } from './auth.guard';

class UserDto {
  @IsNotEmpty()
  username: string;

  @IsNotEmpty()
  password: string;
}

class ChangePassDto {
  @IsNotEmpty()
  oldPass: string;

  @IsNotEmpty()
  password: string;
}

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('sign-in')
  signIn(@Body() userDto: UserDto) {
    return this.authService.signIn(userDto.username, userDto.password);
  }

  @UseGuards(AuthGuard)
  @Get('validate')
  validate(@Request() req) {
    return req.user;
  }

  @UseGuards(AuthGuard)
  @Post('change-password')
  changePasswork(@Request() req, @Body() userDto: ChangePassDto) {
    return this.authService.changePassword(
      req?.user?.id,
      userDto.oldPass,
      userDto.password,
    );
  }
}
