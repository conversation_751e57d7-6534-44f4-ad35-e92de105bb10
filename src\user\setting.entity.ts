import { IsNotEmpty } from 'class-validator';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('Setting')
export class Setting {
  @PrimaryGeneratedColumn('increment')
  public id: number;

  @IsNotEmpty()
  @Column()
  public minPrice: number;

  @IsNotEmpty()
  @Column()
  public convertCusReq: number;

  @IsNotEmpty()
  @Column()
  public applyStart: number;

  @IsNotEmpty()
  @Column()
  public applyEnd: number;

  public isDateApplyRule: boolean = false;
  public applyRuleStaffType: number[] = [];
}
