import { IsNotEmpty } from 'class-validator';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Staff } from './staff.entity';

@Entity('StaffWork')
export class StaffWork {
  @PrimaryGeneratedColumn('increment')
  public id: number;

  @IsNotEmpty()
  @Column()
  public staffId: number;

  @OneToOne(() => Staff)
  @JoinColumn()
  public staff: Staff;

  @IsNotEmpty()
  @Column()
  public timeStart: Date;

  @Column()
  public turn: number;

  @Column()
  public realTurn: number;

  @Column()
  public specTurn: number;

  @Column()
  public customersReq: number;

  @Column()
  public isLeave: boolean;

  @Column()
  public onWorking: boolean;

  @Column()
  public isBusy: boolean;

  @Column()
  public startOn: Date;

  @Column()
  public busyAt: Date;

  @Column()
  public busyEndAt: Date;
}
