import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class TaskTypeCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

export class TaskTypeUpdateDto extends TaskTypeCreateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class TaskTypeDeleteDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class TaskCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsNumber()
  time: number;

  @IsNotEmpty()
  @IsNumber()
  price: number;

  @IsNotEmpty()
  @IsNumber()
  taskTypeId: number;
}

export class TaskUpdateDto extends TaskCreateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class TaskDeleteDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class TaskSortDto {
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  orders: number[];
}
