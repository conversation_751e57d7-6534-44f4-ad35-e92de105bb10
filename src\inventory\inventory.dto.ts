import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { TransactionType } from './common/inventory.contants';

export class TransactionCreateDto {
  @IsNotEmpty()
  @IsEnum(TransactionType)
  transactionType: TransactionType;

  @IsOptional()
  @IsString()
  note?: string;

  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TransactionDetailDto)
  details: TransactionDetailDto[];

  @IsOptional()
  @IsNumber()
  staffId?: number;
}

export class TransactionDetailDto {
  @IsNotEmpty()
  @IsNumber()
  productId: number;

  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @IsOptional()
  @IsString()
  note?: string;
}

export class TransactionHistoryDto {
  @IsNumber()
  fromDateOffset: number;

  @IsOptional()
  @IsNumber()
  toDateOffset?: number;

  @IsNumber()
  productId: number;

  @IsOptional()
  @IsEnum(TransactionType)
  transactionType?: TransactionType;

  @IsOptional()
  @IsString()
  staffName?: string;

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @Transform(({ obj, key }) => obj[key] === 'true')
  @IsBoolean()
  asc?: boolean;
}

export class TransactionGroupByTransactionTypeDto {
  @IsNumber()
  productId: number;

  @IsNumber()
  fromDateOffset: number;

  @IsOptional()
  @IsNumber()
  toDateOffset?: number;
}

export class TransactionGroupDto {
  @IsNumber()
  productId: number;

  @IsNumber()
  fromDateOffset: number;

  @IsOptional()
  @IsNumber()
  toDateOffset?: number;

  @IsOptional()
  @IsString()
  groupBy?: 'day' | 'week' | 'month' | 'year';

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;
}

export class CreateProductDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  unit: string;

  @IsNotEmpty()
  @IsNumber()
  warningQuantity: number;

  @IsNotEmpty()
  @IsNumber()
  qualifiedQuantity: number;

  @IsNotEmpty()
  @IsNumber()
  productTypeId: number;
}

export class UpdateProductDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  unit: string;

  @IsNotEmpty()
  @IsNumber()
  warningQuantity: number;

  @IsNotEmpty()
  @IsNumber()
  qualifiedQuantity: number;
}

export class ProductTypeCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

export class ProductTypeUpdateDto extends ProductTypeCreateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class ProductTypeDeleteDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class ProductSortDto {
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  orders: number[];
}
