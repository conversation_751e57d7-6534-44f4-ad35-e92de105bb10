import { DataSource } from 'typeorm';
import { env } from '../env';
import { constants } from '../constants';
import { addTransactionalDataSource } from 'typeorm-transactional';

export const databaseProviders = [
  {
    provide: constants.db.DATA_SOURCE,
    useFactory: async () => {
      const dataSource = new DataSource({
        type: env.db.type as any,
        host: env.db.host,
        port: env.db.port,
        username: env.db.username,
        password: env.db.password,
        database: env.db.database,
        synchronize: env.db.synchronize,
        logging: env.db.logging,
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        timezone: 'Z',
      });

      const _dataSource = addTransactionalDataSource(dataSource);
      await _dataSource.initialize();

      return _dataSource;
    },
  },
];

// await dataSource.manager.query(`SET time_zone = '-04:00';`);
// const timezone = await dataSource.manager.query(
//   `SELECT @@global.time_zone, @@session.time_zone;`,
// );
// console.log('Timezone:', timezone);
