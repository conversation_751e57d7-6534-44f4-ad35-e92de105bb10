import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { WorkService } from './work.service';
import {
  QueryAllDto,
  WorkContinueDto,
  WorkDeleteDto,
  WorkEndDto,
  WorkSkipDto,
  WorkStartDto,
  WorkTerminateDto,
  WorkUpdateDto,
} from './work.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('work')
export class WorkController {
  constructor(private readonly workService: WorkService) {}

  @Get('')
  getAll(@Query() query: QueryAllDto) {
    return this.workService.findAll({ ...query });
  }

  @Post('start')
  start(@Body() workDto: WorkStartDto) {
    return this.workService.start(
      workDto.staffId,
      workDto.tasks,
      workDto.customerRequest,
      workDto.startAfterSec > 0 ? workDto.startAfterSec : 0,
    );
  }

  @Post('update')
  update(@Body() workDto: WorkUpdateDto) {
    return this.workService.update(
      workDto.id,
      workDto.staffId,
      workDto.tasks,
      workDto.customerRequest,
      workDto.secOffset > 0 ? workDto.secOffset : 0,
    );
  }

  @Post('end')
  end(@Body() workDto: WorkEndDto) {
    return this.workService.end(workDto.staffId);
  }

  @Post('terminate')
  terminate(@Body() workDto: WorkTerminateDto) {
    return this.workService.terminate(workDto.staffId);
  }

  @Post('delete')
  delete(@Body() workDto: WorkDeleteDto) {
    return this.workService.deleteWork(workDto.id);
  }

  @Post('skip')
  skip(@Body() workDto: WorkSkipDto) {
    return this.workService.skipWork(workDto.staffId);
  }

  @Post('continue')
  continueWork(@Body() workDto: WorkContinueDto) {
    return this.workService.continueWork(workDto.id);
  }
}
