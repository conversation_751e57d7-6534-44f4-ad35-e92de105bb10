import { DataSource } from 'typeorm';
import { constants as commonConstants } from 'src/constants';
import { StaffWork } from 'src/staff/staff-work.entity';
import { Staff } from 'src/staff/staff.entity';
import { StaffType } from 'src/staff/staff-type.entity';

export const newProviders = [
  {
    provide: commonConstants.db.STAFF_WORK_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(StaffWork),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: commonConstants.db.STAFF_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Staff),
    inject: [commonConstants.db.DATA_SOURCE],
  },
  {
    provide: commonConstants.db.STAFF_TYPE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(StaffType),
    inject: [commonConstants.db.DATA_SOURCE],
  },
];
