import * as dotenv from 'dotenv';
import * as path from 'path';

function getOsEnv(key: string): string {
  if (typeof process.env[key] === 'undefined') {
    throw new Error(`Environment variable ${key} is not set.`);
  }

  return process.env[key] as string;
}

function getOsEnvOptional(key: string): string | undefined {
  return process.env[key];
}

// function getPath(path: string): string {
//   return process.env.NODE_ENV === 'production'
//     ? join(process.cwd(), path.replace('src/', 'dist/').slice(0, -3) + '.js')
//     : join(process.cwd(), path);
// }

// function getPaths(paths: string[]): string[] {
//   return paths.map((p) => getPath(p));
// }

// function getOsPath(key: string): string {
//   return getPath(getOsEnv(key));
// }

// function getOsPaths(key: string): string[] {
//   return getPaths(getOsEnvArray(key));
// }

// function getOsEnvArray(key: string, delimiter: string = ','): string[] {
//   return (process.env[key] && process.env[key].split(delimiter)) || [];
// }

function toNumber(value: string): number {
  return parseInt(value, 10);
}

function toBool(value: string): boolean {
  return value === 'true';
}

function normalizePort(port: string): number | string {
  const parsedPort = parseInt(port, 10);
  if (isNaN(parsedPort)) {
    // named pipe
    return port;
  }
  if (parsedPort >= 0) {
    // port number
    return parsedPort;
  }
  return 3000;
}

/**
 * Load .env file or for tests the .env.test file.
 */
dotenv.config({
  path: path.join(
    process.cwd(),
    `.env${process.env.NODE_ENV ? `.${process.env.NODE_ENV}` : ''}`,
  ),
});

/**
 * Environment variables
 */
export const env = {
  node: process.env.NODE_ENV || 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
  isDevelopment: process.env.NODE_ENV === 'development',
  setting: {
    timezone: getOsEnv('TIMEZONE'),
  },
  app: {
    name: getOsEnv('APP_NAME'),
    host: getOsEnv('APP_HOST'),
    schema: getOsEnv('APP_SCHEMA'),
    routePrefix: getOsEnv('APP_ROUTE_PREFIX'),
    port: normalizePort(process.env.PORT || getOsEnv('APP_PORT')),
  },
  db: {
    type: getOsEnv('TYPEORM_CONNECTION'),
    host: getOsEnvOptional('TYPEORM_HOST'),
    port: toNumber(getOsEnvOptional('TYPEORM_PORT')),
    username: getOsEnvOptional('TYPEORM_USERNAME'),
    password: getOsEnvOptional('TYPEORM_PASSWORD'),
    database: getOsEnv('TYPEORM_DATABASE'),
    synchronize: toBool(getOsEnvOptional('TYPEORM_SYNCHRONIZE')),
    logging: toBool(getOsEnvOptional('TYPEORM_LOGGING')),
  },
  jwt: {
    secretKey: getOsEnv('JWT_SECRET_KEY'),
    expiresIn: getOsEnvOptional('JWT_EXPIRES'),
  },
};
