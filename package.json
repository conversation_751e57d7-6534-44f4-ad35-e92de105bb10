{"name": "nest-typescript-starter", "private": true, "version": "1.0.0", "description": "Nest TypeScript starter repository", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.3.2", "@nestjs/core": "^10.3.2", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.3.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "moment-timezone": "^0.5.45", "mysql2": "^3.10.1", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-transactional": "^0.5.0"}, "devDependencies": {"@nestjs/cli": "^10.3.1", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.3.2", "@swc/cli": "^0.3.9", "@swc/core": "^1.4.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.16", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@swc/core", "bcrypt"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}