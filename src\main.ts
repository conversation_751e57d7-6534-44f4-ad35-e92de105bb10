import { env } from './env';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { ValidationPipe } from '@nestjs/common';
import { delay } from './middleware/delay.middleware';
import {
  initializeTransactionalContext,
  StorageDriver,
} from 'typeorm-transactional';
import { format } from 'date-fns';

async function bootstrap() {
  initializeTransactionalContext({ storageDriver: StorageDriver.AUTO });

  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn'],
  });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  app.setGlobalPrefix(env.app.routePrefix);
  app.enableCors();
  app.use(delay);
  await app.listen(env.app.port);

  console.log(
    format(new Date(), '[yyyy-MM-dd HH:mm:ss]'),
    `: Application ${env.app.name} running at http://localhost:${env.app.port}`,
  );
}

bootstrap();
