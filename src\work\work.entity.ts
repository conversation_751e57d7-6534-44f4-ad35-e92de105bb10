import { IsNotEmpty } from 'class-validator';
import { Staff } from 'src/staff/staff.entity';
import { Task } from 'src/task/task.entity';
import {
  Column,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('Work')
export class Work {
  @PrimaryGeneratedColumn('increment')
  public id: number;

  @IsNotEmpty()
  @Column()
  public staffId: number;

  @OneToOne(() => Staff)
  @JoinColumn()
  public staff: Staff;

  @ManyToMany(() => Task, {
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'WorkDetail' })
  public tasks: Task[];

  @Column()
  public timeStart: Date;

  @Column()
  public timeEnd: Date;

  @Column()
  public time: number;

  @Column()
  public customerRequest: boolean;

  @Column()
  public isSkip: boolean;

  @Column()
  public isSpecTurn: boolean;

  @Column()
  public onWorking: boolean;

  @Column()
  public isDelete: boolean;
}
