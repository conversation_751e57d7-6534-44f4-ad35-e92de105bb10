import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { constants } from 'src/constants';

import { Between, LessThan, Repository } from 'typeorm';
import { Staff } from './staff.entity';
import { WorkService } from 'src/work/work.service';
import { StaffWork } from './staff-work.entity';
import { differenceInCalendarDays, subDays } from 'date-fns';
import { endOfDay, isDateApplyRule, safeEndOfDay, startOfDay } from 'src/utils';
import { Work } from 'src/work/work.entity';
import { StaffOrder } from './staff-order.entity';
import { StaffType } from './staff-type.entity';
import { Transactional } from 'typeorm-transactional';
import { UserService } from 'src/user/user.service';
import { BusyHistory } from './busy-history.entity';

export enum WORK_STATUS {
  SPEC_TURN = 0,
  TURN = 1,
  CUSTOMER_REQ = 2,
  NOT_CHANGE = 3,
}

const ORDER_BY_QUERY = [
  'CASE yda.isLeave WHEN 1 THEN staffOrder.orderIndex END',
  'CASE yda.isLeave WHEN 0 THEN staffOrder.orderIndex END',
];

@Injectable()
export class StaffService {
  constructor(
    @Inject(constants.db.STAFF_REPOSITORY)
    private staffRepository: Repository<Staff>,

    @Inject(constants.db.STAFF_WORK_REPOSITORY)
    private staffWorkRepository: Repository<StaffWork>,

    @Inject(constants.db.STAFF_ORDER_REPOSITORY)
    private orderRepository: Repository<StaffOrder>,

    @Inject(constants.db.STAFF_TYPE_REPOSITORY)
    private staffTypeRepository: Repository<StaffType>,

    @Inject(constants.db.BUSY_HISTORY_REPOSITORY)
    private busyRepo: Repository<BusyHistory>,

    @Inject(forwardRef(() => WorkService))
    private workService: WorkService,

    @Inject(UserService)
    private userService: UserService,
  ) {}

  async findById(id: number) {
    return this.staffRepository.findOne({
      where: { id },
    });
  }

  async findAll() {
    return this.staffRepository.find({
      where: { isDelete: false },
      order: { name: 'ASC' },
    });
  }

  async getOrderByDate(date: Date) {
    return this.orderRepository.find({
      where: {
        timeStart: Between(startOfDay(date), endOfDay(date)),
      },
      order: {
        orderIndex: 'ASC',
      },
    });
  }

  @Transactional()
  async saveOrderBySystem(date: Date) {
    const _date = new Date(date);
    const _yDate = subDays(new Date(_date), 1);

    const staffs = await this.staffRepository
      .createQueryBuilder('staff')
      .where('staff.isDelete = :isDelete', { isDelete: false })
      .leftJoinAndMapOne(
        'staff.yda',
        StaffWork,
        'yda',
        '(yda.staffId = staff.id) AND (yda.timeStart BETWEEN :ydaStart AND :ydaEnd)',
        { ydaStart: startOfDay(_yDate), ydaEnd: endOfDay(_yDate) },
      )
      .leftJoinAndMapOne(
        'staff.staffOrder',
        StaffOrder,
        'staffOrder',
        '(staffOrder.staffId = staff.id) AND (staffOrder.timeStart BETWEEN :todayStart AND :todayEnd)',
        { todayStart: startOfDay(_yDate), todayEnd: endOfDay(_yDate) },
      )
      .orderBy(`yda.id IS NULL`, 'ASC')
      .orderBy('yda.turn', 'ASC')
      .addOrderBy('yda.isLeave', 'DESC')
      .addOrderBy(ORDER_BY_QUERY[0], 'ASC')
      .addOrderBy(ORDER_BY_QUERY[1], 'DESC')
      .addOrderBy('staffOrder.orderIndex', 'ASC')
      .getMany();

    const _orders = await this.getOrderByDate(_date);
    if (_orders.length > 0)
      await this.orderRepository.delete(_orders.map((el) => el.id));

    const _newOrders: StaffOrder[] = staffs.map((staff, index) => {
      const _order = new StaffOrder();
      _order.staffId = staff.id;

      _order.orderIndex = index;
      _order.timeStart = _date;
      return _order;
    });

    return this.orderRepository.save(_newOrders);
  }

  @Transactional()
  async saveOlderWork(date: Date) {
    const _date = new Date(date);
    const _yDate = subDays(new Date(_date), 1);

    const _staffWorks = await this.staffWorkRepository.find({
      where: [
        { timeStart: LessThan(endOfDay(_yDate)), isBusy: true },
        { timeStart: LessThan(endOfDay(_yDate)), onWorking: true },
      ],
    });

    for (const _staffWork of _staffWorks) {
      if (_staffWork.isBusy) {
        const dateOffset = differenceInCalendarDays(new Date(), _staffWork.timeStart);
        const staffId = _staffWork.staffId;
        await this.saveStatusStaffWork(staffId, _staffWork.isLeave, false, dateOffset, _staffWork.id,);
      } else if (_staffWork.onWorking) {
        await this.workService.end(
          _staffWork.staffId,
          endOfDay(_staffWork.timeStart),
        );
      }
    }

    return _staffWorks;
  }

  async findAllByOrder(date: Date) {
    const _date = new Date(date);

    return this.staffRepository
      .createQueryBuilder('staff')
      .where('staff.isDelete = :isDelete', { isDelete: false })
      .leftJoinAndMapOne(
        'staff.today',
        StaffWork,
        'today',
        '(today.staffId = staff.id) AND (today.timeStart BETWEEN :todayStart AND :todayEnd)',
        { todayStart: startOfDay(_date), todayEnd: endOfDay(_date) },
      )
      .leftJoinAndMapOne(
        'staff.staffOrder',
        StaffOrder,
        'staffOrder',
        '(staffOrder.staffId = staff.id) AND (staffOrder.timeStart BETWEEN :orderStart AND :orderEnd)',
        { orderStart: startOfDay(_date), orderEnd: endOfDay(_date) },
      )
      .leftJoinAndMapMany(
        'staff.works',
        Work,
        'works',
        '(works.staffId = staff.id) AND (works.timeStart BETWEEN :workStart AND :workEnd) AND (works.isDelete=0)',
        { workStart: startOfDay(_date), workEnd: endOfDay(_date) },
      )
      .leftJoinAndSelect('works.tasks', 'tasks')
      .orderBy('staffOrder.orderIndex', 'ASC')
      .addOrderBy('works.timeStart', 'ASC')
      .getMany();
  }

  async findAllWithInfo(dateOffset = 0) {
    const _date = subDays(new Date(), dateOffset);
    const _orders = await this.getOrderByDate(_date);

    if (!_orders || _orders.length < 1) {
      if (dateOffset == 0) await this.saveOlderWork(_date);

      await this.saveOrderBySystem(_date);
    }

    return this.findAllByOrder(_date);
  }

  async findAllWithInfoRange(fromDateOffset = 0, toDateOffset: number) {
    const _toDateOffset = isNaN(toDateOffset) ? fromDateOffset : toDateOffset;
    const _fromDate = subDays(new Date(), fromDateOffset);
    const _toDate = subDays(new Date(), _toDateOffset);

    return this.staffRepository
      .createQueryBuilder('staff')
      .where('staff.isDelete = :isDelete', { isDelete: false })
      .leftJoinAndMapMany(
        'staff.info',
        StaffWork,
        'info',
        '(info.staffId = staff.id) AND (info.timeStart BETWEEN :todayStart AND :todayEnd)',
        { todayStart: startOfDay(_fromDate), todayEnd: endOfDay(_toDate) },
      )
      .orderBy('staff.name', 'ASC')
      .getMany();
  }

  async findOneWithInfo(id: number) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _staffWork = await this.getStaffWorkByDate(_staff.id, new Date());

    const _works = await this.workService.findAll({
      staffId: id,
      fromDate: new Date(),
    });

    return {
      ..._staff,
      today: _staffWork,
      works: _works,
    };
  }

  @Transactional()
  async saveStatusStaffWork(
    id: number,
    isLeave = false,
    isBusy = false,
    dateOffset = 0,
    _staffWorkId?: number,
  ) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _date = subDays(new Date(), dateOffset);
    let _staffWork = await this.getStaffWorkByDate(_staff.id, _date);

    if (_staffWorkId) {
      if (_staffWork.id != _staffWorkId) {
        const _swById = await this.staffWorkRepository.findOne({
          where: { id: _staffWorkId },
        });

        console.log(`[WARNING] Wrong staff work: ${_swById.id} != ${_staffWork.id}`);
        console.log(`[WARNING] Wrong staff work: ${_swById.timeStart} != ${_staffWork.timeStart}`);

        _staffWork = _swById;
      }
    }

    if (typeof isLeave == 'boolean') {
      if (isLeave != _staffWork.isLeave) {
        _staffWork.isLeave = isLeave;
      }
    }

    if (typeof isBusy == 'boolean') {
      if (isBusy != _staffWork.isBusy) {
        if (isBusy) {
          _staffWork.busyAt = new Date();
          _staffWork.busyEndAt = null;
        } else if (_staffWork.busyAt && _staffWork.isBusy) {
          _staffWork.busyEndAt =
            dateOffset == 0 ? new Date() : safeEndOfDay(_date);
          const busyTimeMilis =
            _staffWork.busyEndAt.getTime() - _staffWork.busyAt.getTime();

          await this.busyRepo.save({
            staffId: _staff.id,
            busyAt: _staffWork.busyAt,
            busyEndAt: _staffWork.busyEndAt,
            busyTime: Math.floor(busyTimeMilis / 1000),
          });
        }

        _staffWork.isBusy = isBusy;
      }
    }

    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async save(data: any): Promise<Staff> {
    return this.staffRepository.save(data);
  }

  @Transactional()
  async startWork(id: number, date: Date) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _staffWork = await this.getStaffWorkByDate(_staff.id, date);
    if (_staffWork.onWorking) throw new BadRequestException('Staff on working');
    if (_staffWork.isLeave) throw new BadRequestException('Staff is leave');
    if (_staffWork.isBusy) throw new BadRequestException('Staff is busy');

    _staffWork.startOn = date;
    _staffWork.onWorking = true;
    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async updateStartOn(id: number, date: Date) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _staffWork = await this.getStaffWorkByDate(_staff.id, date);
    _staffWork.startOn = date;
    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async getStaffWorkByDate(id: number, date: Date) {
    let _staffWork = await this.staffWorkRepository.findOne({
      where: {
        staffId: id,
        timeStart: Between(startOfDay(date), endOfDay(date)),
      },
    });

    if (!_staffWork) {
      _staffWork = new StaffWork();

      _staffWork.timeStart = date;
      _staffWork.isLeave = false;
      _staffWork.turn = 0;
      _staffWork.realTurn = 0;
      _staffWork.customersReq = 0;
      _staffWork.staffId = id;

      return await this.staffWorkRepository.save(_staffWork);
    }

    return _staffWork;
  }

  @Transactional()
  async calTurn(
    workStatus: WORK_STATUS,
    staffWork: StaffWork,
    addNumber = 1,
    date = new Date(),
  ) {
    const _staff = await this.findById(staffWork.staffId);
    if (!_staff) return;

    switch (workStatus) {
      case WORK_STATUS.CUSTOMER_REQ:
        staffWork.customersReq += addNumber;
        break;
      case WORK_STATUS.SPEC_TURN:
        staffWork.specTurn += addNumber;
        break;
      case WORK_STATUS.TURN:
        staffWork.realTurn += addNumber;
        break;
    }

    const _setting = await this.userService.getSetting();

    let _incTurn = 0;
    if (_setting.convertCusReq > 0) {
      if (isDateApplyRule(date, _setting.applyStart, _setting.applyEnd)) {
        const _isApplyForStaffType =
          _setting.applyRuleStaffType.filter((el) => el == _staff.staffTypeId)
            .length > 0;

        if (_isApplyForStaffType) {
          _incTurn = Math.floor(
            staffWork.customersReq / _setting.convertCusReq,
          );
        }
      }
    }

    staffWork.turn = staffWork.realTurn + _incTurn;
  }

  @Transactional()
  async moveTurnToStaff(
    fromId: number,
    toId: number,
    date: Date,
    from: WORK_STATUS,
    to: WORK_STATUS,
  ) {
    if (fromId == toId && from == to) return;

    const _fromStaffWork = await this.getStaffWorkByDate(fromId, date);
    await this.calTurn(from, _fromStaffWork, -1);

    if (fromId == toId) {
      await this.calTurn(to, _fromStaffWork);
    } else {
      const _toStaffWork = await this.getStaffWorkByDate(toId, date);
      await this.calTurn(to, _toStaffWork);
      await this.staffWorkRepository.save(_toStaffWork);
    }

    return this.staffWorkRepository.save(_fromStaffWork);
  }

  @Transactional()
  async endWork(id: number, date: Date, workStatus: WORK_STATUS) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _staffWork = await this.getStaffWorkByDate(_staff.id, date);
    _staffWork.onWorking = false;

    await this.calTurn(workStatus, _staffWork, 1, date);
    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async removeWorking(id: number, date: Date) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const _staffWork = await this.getStaffWorkByDate(_staff.id, date);
    _staffWork.onWorking = false;

    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async subTurn(id: number, date: Date, workStatus: WORK_STATUS) {
    const _staffWork = await this.getStaffWorkByDate(id, date);
    if (!_staffWork) return;

    await this.calTurn(workStatus, _staffWork, -1);
    return this.staffWorkRepository.save(_staffWork);
  }

  @Transactional()
  async skipWork(id: number, date: Date) {
    const _staff = await this.findById(id);
    if (!_staff) throw new BadRequestException('Staff not found');

    const staffWork = await this.getStaffWorkByDate(id, date);
    staffWork.turn += 1;
    staffWork.realTurn += 1;

    return this.staffWorkRepository.save(staffWork);
  }

  @Transactional()
  async saveOrderByUser(orders: number[], dateOffset = 0) {
    const _date = subDays(new Date(), dateOffset);
    const _staffOrders = await this.getOrderByDate(_date);
    const _excludeOrders = _staffOrders
      .filter((x) => !orders.includes(x.id))
      .map((el) => el.id);

    if (_excludeOrders.length > 0)
      await this.orderRepository.delete(_excludeOrders);

    const _newOrders: StaffOrder[] = orders.map((el, index) => {
      let _order = _staffOrders.find((x) => x.staffId == el);

      if (!_order) {
        _order = new StaffOrder();
        _order.staffId = el;
      }

      _order.orderIndex = index;
      _order.timeStart = new Date();
      return _order;
    });

    return this.orderRepository.save(_newOrders);
  }

  @Transactional()
  async autoSort(dateOffset = 0) {
    const _date = subDays(new Date(), dateOffset);
    return this.saveOrderBySystem(_date);
  }

  async findAllType() {
    return this.staffTypeRepository.find({ where: { isDelete: false } });
  }

  @Transactional()
  async saveType(data: any) {
    return this.staffTypeRepository.save(data);
  }

  async findBusyHistory(staffId: number, dateOffset = 0) {
    const _date = subDays(new Date(), dateOffset);
    return this.busyRepo.find({
      where: {
        ...(staffId && { staffId: staffId }),
        busyAt: Between(startOfDay(_date), endOfDay(_date)),
      },
      order: { busyAt: 'DESC' },
    });
  }
}
