CREATE TABLE User(
    id int NOT NULL AUTO_INCREMENT,
    username varchar(255),
    password varchar(255),
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE StaffType(
    id int NOT NULL AUTO_INCREMENT,
    name varchar(255),
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE Staff(
    id int NOT NULL AUTO_INCREMENT,
    name varchar(255),
    staffTypeId int NOT NULL,
    onWorking boolean DEFAULT false,
    isBusy boolean DEFAULT false,
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (staffTypeId) REFERENCES StaffType(id)
);

CREATE TABLE StaffOrder(
    id int NOT NULL AUTO_INCREMENT,
    staffId int NOT NULL,
    orderIndex int NOT NULL,
    timeStart DATETIME NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (staffId) REFERENCES Staff(id)
);

CREATE TABLE StaffWork(
    id int NOT NULL AUTO_INCREMENT,
    staffId int NOT NULL,
    turn int NOT NULL DEFAULT 0,
    specTurn int NOT NULL DEFAULT 0,
    customersReq int NOT NULL DEFAULT 0,
    isLeave boolean NOT NULL DEFAULT false,
    timeStart DATETIME NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (staffId) REFERENCES Staff(id)
);

CREATE TABLE TaskType(
    id int NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE Task(
    id int NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    time int,
    price int,
    taskTypeId int,
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (taskTypeId) REFERENCES TaskType(id)
);

CREATE TABLE Work(
    id int NOT NULL AUTO_INCREMENT,
    staffId int NOT NULL,
    timeStart DATETIME,
    timeEnd DATETIME,
    time int,
    isSkip boolean DEFAULT false,
    isSpecTurn boolean DEFAULT false,
    customerRequest boolean DEFAULT false,
    onWorking boolean DEFAULT true,
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (staffId) REFERENCES Staff(id)
);

CREATE TABLE WorkDetail(
    id int NOT NULL AUTO_INCREMENT,
    workId int NOT NULL,
    taskId int NOT NULL,
    isDelete boolean DEFAULT false,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (workId) REFERENCES Work(id),
    FOREIGN KEY (taskId) REFERENCES Task(id)
);

ALTER TABLE Task ADD orderIndex int;

ALTER TABLE StaffWork ADD startOn DATETIME;
ALTER TABLE StaffWork ADD onWorking boolean DEFAULT false;
ALTER TABLE StaffWork ADD isBusy boolean DEFAULT false;

ALTER TABLE Task ADD isLove boolean DEFAULT false;

-- 08/12/2024
ALTER TABLE StaffWork ADD cusReqConvert int NOT NULL DEFAULT 0;

CREATE TABLE Setting(
    id int NOT NULL AUTO_INCREMENT,
    minPrice int NOT NULL DEFAULT 3000,
    convertCusReq int NOT NULL DEFAULT 0,
    applyStart int NOT NULL DEFAULT 1,
    applyEnd int NOT NULL DEFAULT 7,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE ApplyRule(
    id int NOT NULL AUTO_INCREMENT,
    staffTypeId int NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

ALTER TABLE StaffWork ADD realTurn int NOT NULL DEFAULT 0;
