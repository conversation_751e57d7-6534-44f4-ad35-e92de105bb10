import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
} from 'class-validator';

export class WorkStartDto {
  @IsNotEmpty()
  @IsNumber()
  staffId: number;

  @IsOptional()
  @IsNumber({}, { each: true })
  tasks?: number[];

  @IsOptional()
  @IsBoolean()
  customerRequest?: boolean;

  @IsOptional()
  @IsNumber()
  startAfterSec?: number;
}

export class WorkUpdateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsNumber()
  staffId: number;

  @IsNotEmpty()
  @IsNumber({}, { each: true })
  tasks: number[];

  @IsOptional()
  @IsBoolean()
  customerRequest?: boolean;

  @IsOptional()
  @IsNumber()
  secOffset?: number;
}

export class WorkEndDto {
  @IsNotEmpty()
  @IsNumber()
  staffId: number;
}

export class WorkTerminateDto {
  @IsNotEmpty()
  @IsNumber()
  staffId: number;
}

export class WorkSkipDto {
  @IsNotEmpty()
  @IsNumber()
  staffId: number;
}

export class WorkDeleteDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class QueryAllDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  staffId: number;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  fromDate: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  toDate: Date;
}

export class WorkContinueDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id: number;
}
