import { DataSource } from 'typeorm';
import { User } from './user.entity';
import { constants } from '../constants';
import { Setting } from './setting.entity';
import { ApplyRule } from './apply-rule.entity';
import { Note } from './note.entity';

export const userProviders = [
  {
    provide: constants.db.USER_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(User),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.SETTING_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Setting),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.APPLY_RULE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(ApplyRule),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.NOTE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Note),
    inject: [constants.db.DATA_SOURCE],
  },
];
