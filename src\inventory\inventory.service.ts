import { BadRequestException, Inject } from '@nestjs/common';
import { constants, TransactionType } from './common/inventory.contants';
import { Between, In, IsNull, Like, Repository } from 'typeorm';
import { TransactionReceipt } from './entities/transaction-receipt.entity';
import { TransactionDetail } from './entities/transaction-detail.entity';
import { Product } from './entities/product.entity';
import { TransactionDetailDto } from './inventory.dto';
import { Transactional } from 'typeorm-transactional';
import { subDays } from 'date-fns';
import { endOfDay, startOfDay } from 'src/utils';
import { ProductType } from './entities/product-type.entity';
import { InventoryNote } from './entities/inventory-note.entity';

export class InventoryService {
  constructor(
    @Inject(constants.db.TRANSACTION_RECEIPT_REPOSITORY)
    private transactionReceiptRepo: Repository<TransactionReceipt>,

    @Inject(constants.db.TRANSACTION_DETAIL_REPOSITORY)
    private transactionDetailRepo: Repository<TransactionDetail>,

    @Inject(constants.db.PRODUCT_REPOSITORY)
    private productRepo: Repository<Product>,

    @Inject(constants.db.PRODUCT_TYPE_REPOSITORY)
    private productTypeRepo: Repository<ProductType>,

    @Inject(constants.db.INVENTORY_NOTE_REPOSITORY)
    private noteRepo: Repository<InventoryNote>,
  ) {}

  async createProduct(
    name: string,
    unit: string,
    warningQuantity: number,
    qualifiedQuantity: number,
    productTypeId: number,
  ): Promise<Product> {
    const _product = new Product();
    _product.name = name;
    _product.unit = unit;
    _product.warningQuantity = warningQuantity;
    _product.qualifiedQuantity = qualifiedQuantity;
    _product.productTypeId = productTypeId;
    return this.productRepo.save(_product);
  }

  async updateProduct(
    id: number,
    name: string,
    unit: string,
    warningQuantity: number,
    qualifiedQuantity: number,
  ): Promise<Product> {
    const _product = await this.productRepo.findOne({ where: { id } });
    _product.name = name;
    _product.unit = unit;
    _product.warningQuantity = warningQuantity;
    _product.qualifiedQuantity = qualifiedQuantity;
    return this.productRepo.save(_product);
  }

  async createProductType(name: string): Promise<ProductType> {
    const _productType = new ProductType();
    _productType.name = name;
    return this.productTypeRepo.save(_productType);
  }

  async findAllProduct() {
    return this.productRepo.find({
      where: { deletedAt: IsNull(), productType: { deletedAt: IsNull() } },
      relations: ['productType'],
      order: { productOrder: 'ASC' },
    });
  }

  async findWarningProduct() {
    return this.productRepo
      .createQueryBuilder('p')
      .leftJoinAndSelect('p.productType', 'pt')
      .where('p.deletedAt IS NULL')
      .andWhere('pt.deletedAt IS NULL')
      .andWhere('p.quantity < p.warningQuantity')
      .orderBy('p.productOrder', 'ASC')
      .getMany();
  }

  async findRequestProduct() {
    return this.productRepo
      .createQueryBuilder('p')
      .leftJoinAndSelect('p.productType', 'pt')
      .where('p.deletedAt IS NULL')
      .andWhere('pt.deletedAt IS NULL')
      .andWhere('p.quantity < p.qualifiedQuantity')
      .orderBy('p.productOrder', 'ASC')
      .getMany();
  }

  async deleteProduct(id: number) {
    const _product = await this.productRepo.findOne({ where: { id } });
    _product.deletedAt = new Date();
    return this.productRepo.save(_product);
  }

  @Transactional()
  async createTransaction(
    transactionType: TransactionType,
    details: TransactionDetailDto[],
    note: string = '',
    staffId: number = null,
  ): Promise<TransactionReceipt> {
    if (details.length < 1) {
      throw new BadRequestException('Must have at least one detail');
    }

    const _transaction = new TransactionReceipt();
    _transaction.transactionDate = new Date();
    _transaction.transactionType = transactionType;
    _transaction.note = note;

    if (staffId) _transaction.staffId = staffId;
    const _transactionSaved = await this.transactionReceiptRepo.save(_transaction);

    const _transactionDetails: TransactionDetail[] = [];
    const _products: Product[] = [];

    const _mergeDetails: TransactionDetailDto[] = details.reduce(
      (acc: TransactionDetailDto[], cur: TransactionDetailDto) => {
        const _exist = acc.find((x) => x.productId == cur.productId);
        if (!_exist) return [...acc, cur];

        _exist.quantity += cur.quantity;
        if (cur.note && cur.note != '') {
          if (!_exist.note) {
            _exist.note = cur.note;
          } else {
            _exist.note += ';' + cur.note;
          }
        }

        return acc;
      },
      [],
    );

    for (const _detail of _mergeDetails) {
      const _product = await this.productRepo.findOne({
        where: { id: _detail.productId },
      });

      if (!_product) {
        throw new BadRequestException(`Product not found: ${_detail.productId}`);
      }

      const _transactionDetail = new TransactionDetail();
      _transactionDetail.transactionReceiptId = _transactionSaved.id;
      _transactionDetail.product = _product;
      _transactionDetail.quantity = _detail.quantity;
      _transactionDetail.note = _detail.note;
      _transactionDetails.push(_transactionDetail);

      if (_product.quantity < _detail.quantity && transactionType == TransactionType.EXPORT) {
        throw new BadRequestException(`Not enough quantity for "${_product.name}"`);
      }

      _product.quantity =
        transactionType == TransactionType.IMPORT
          ? _product.quantity + _detail.quantity
          : _product.quantity - _detail.quantity;

      _products.push(_product);
    }

    await this.transactionDetailRepo.save(_transactionDetails);
    if (transactionType == TransactionType.IMPORT || transactionType == TransactionType.EXPORT)
      await this.productRepo.save(_products);
    return _transactionSaved;
  }

  async getTransactionHistory(
    productId: number,
    fromDateOffset = 0,
    toDateOffset = 0,
    transactionType?: TransactionType,
    staffName?: string,
    page = 1,
    limit = 10,
  ) {
    const _haveAdmin = staffName && 'manager'.includes(staffName.toLowerCase());
    const txTypeWhere = transactionType
      ? { transactionType }
      : { transactionType: In([TransactionType.IMPORT, TransactionType.EXPORT]) };

    const txDateWhere = {
      transactionDate: Between(
        startOfDay(subDays(new Date(), fromDateOffset)),
        endOfDay(subDays(new Date(), toDateOffset)),
      ),
    };

    const _where = {
      productId: productId,
      transactionReceipt: {
        ...txTypeWhere,
        ...txDateWhere,
        ...(staffName && { staff: { name: Like(`%${staffName}%`) } }),
      },
    };

    const _whereAdmin = {
      productId: productId,
      transactionReceipt: {
        ...txTypeWhere,
        ...txDateWhere,
        staffId: IsNull(),
      },
    };

    const [data, total] = await this.transactionDetailRepo.findAndCount({
      relations: ['transactionReceipt', 'transactionReceipt.staff'],
      order: { transactionReceipt: { transactionDate: 'DESC' } },
      where: _haveAdmin ? [_where, _whereAdmin] : _where,
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data,
      total,
      limit,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getTransactionGroupByStaff(
    productId: number,
    fromDateOffset = 0,
    toDateOffset = 0,
    transactionType?: TransactionType,
    staffName?: string,
    page = 1,
    limit = 10,
    asc = false,
  ) {
    const offset = (page - 1) * limit;

    const baseQuery = this.transactionDetailRepo
      .createQueryBuilder('detail')
      .leftJoin('detail.transactionReceipt', 'transactionReceipt')
      .leftJoin('transactionReceipt.staff', 'staff')
      .andWhere('detail.productId = :productId', { productId });

    if (transactionType) {
      baseQuery.andWhere('transactionReceipt.transactionType = :type', { type: transactionType });
    } else {
      baseQuery.andWhere('transactionReceipt.transactionType IN (:...types)', {
        types: [TransactionType.IMPORT, TransactionType.EXPORT],
      });
    }

    if (staffName) {
      if ('manager'.includes(staffName.toLowerCase())) {
        baseQuery.andWhere('(staff.name LIKE :staffName OR staff.name IS NULL)', {
          staffName: `%${staffName}%`,
        });
      } else {
        baseQuery.andWhere('staff.name LIKE :staffName', { staffName: `%${staffName}%` });
      }
    }

    baseQuery.andWhere({
      transactionReceipt: {
        transactionDate: Between(
          startOfDay(subDays(new Date(), fromDateOffset)),
          endOfDay(subDays(new Date(), toDateOffset)),
        ),
      },
    });

    const dataQuery = baseQuery
      .clone()
      .select('staff.name', 'staffName')
      .addSelect('SUM(detail.quantity)', 'totalQuantity')
      .addSelect('transactionReceipt.transactionType', 'transactionType')
      .groupBy('staff.name')
      .addGroupBy('transactionReceipt.transactionType')
      .orderBy('totalQuantity', asc ? 'ASC' : 'DESC')
      .offset(offset)
      .limit(limit);

    const [data, total] = await Promise.all([
      dataQuery.getRawMany(),
      baseQuery
        .clone()
        .select(
          'COUNT(DISTINCT CONCAT(staff.name, "-", transactionReceipt.transactionType))',
          'count',
        )
        .getRawOne()
        .then((res) => Number(res.count)),
    ]);

    return {
      data,
      total,
      limit,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getTransactionGroupByTransactionType(
    productId: number,
    fromDateOffset = 0,
    toDateOffset = 0,
  ) {
    const baseQuery = this.transactionDetailRepo
      .createQueryBuilder('detail')
      .leftJoin('detail.transactionReceipt', 'transactionReceipt')
      .where('transactionReceipt.transactionType IN (:...types)', {
        types: [TransactionType.IMPORT, TransactionType.EXPORT],
      })
      .andWhere('detail.productId = :productId', { productId })
      .andWhere({
        transactionReceipt: {
          transactionDate: Between(
            startOfDay(subDays(new Date(), fromDateOffset)),
            endOfDay(subDays(new Date(), toDateOffset)),
          ),
        },
      });

    const data = await baseQuery
      .clone()
      .select('transactionReceipt.transactionType', 'transactionType')
      .addSelect('SUM(detail.quantity)', 'totalQuantity')
      .groupBy('transactionReceipt.transactionType')
      .getRawMany();

    return data;
  }

  async getTransactionGroup(
    productId: number,
    fromDateOffset = 0,
    toDateOffset = 0,
    groupBy: 'day' | 'week' | 'month' | 'year' = 'day',
    page = 1,
    limit = 10,
  ) {
    const startDate = startOfDay(subDays(new Date(), fromDateOffset));
    const endDate = endOfDay(subDays(new Date(), toDateOffset));

    const baseQuery = this.transactionDetailRepo
      .createQueryBuilder('detail')
      .leftJoin('detail.transactionReceipt', 'transactionReceipt')
      .where('transactionReceipt.transactionType IN (:...types)', {
        types: [TransactionType.IMPORT, TransactionType.EXPORT],
      })
      .andWhere('detail.productId = :productId', { productId })
      .andWhere('transactionReceipt.transactionDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });

    let groupSelect = '';

    switch (groupBy) {
      case 'day':
        groupSelect = 'DATE(transactionReceipt.transactionDate)';
        break;
      case 'week':
        groupSelect =
          'YEAR(transactionReceipt.transactionDate) * 100 + WEEK(transactionReceipt.transactionDate, 1)'; // year*100+week tránh trùng năm
        break;
      case 'month':
        groupSelect =
          'YEAR(transactionReceipt.transactionDate) * 100 + MONTH(transactionReceipt.transactionDate)';
        break;
      case 'year':
        groupSelect = 'YEAR(transactionReceipt.transactionDate)';
        break;
    }

    baseQuery
      .select(groupSelect, 'groupKey')
      .addSelect('transactionReceipt.transactionType', 'transactionType')
      .addSelect('SUM(detail.quantity)', 'totalQuantity')
      .groupBy('groupKey')
      .addGroupBy('transactionReceipt.transactionType')
      .orderBy('groupKey', 'ASC')
      .addOrderBy('groupKey', 'DESC');

    const offset = (page - 1) * limit;
    const data = await baseQuery.clone().offset(offset).limit(limit).getRawMany();
    const total = (await baseQuery.clone().getRawMany()).length;

    return {
      data,
      total,
      currentPage: page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findAllType() {
    return this.productTypeRepo.find({ where: { deletedAt: IsNull() } });
  }

  @Transactional()
  async saveType(data: any) {
    return this.productTypeRepo.save(data);
  }

  async getTransactionDetail(id: number) {
    return this.transactionReceiptRepo.findOne({
      where: { id },
      relations: ['details', 'details.product', 'staff'],
    });
  }

  async getTransactionRequest() {
    return this.transactionReceiptRepo.find({
      where: { transactionType: TransactionType.REQUEST },
      relations: ['details', 'details.product'],
      order: { transactionDate: 'DESC' },
    });
  }

  async sort(orders: number[]) {
    const _products = await this.productRepo.find({
      where: { deletedAt: IsNull() },
    });

    _products.forEach((el) => {
      const _index = orders.findIndex((order) => order == el.id);
      el.productOrder = _index >= 0 ? _index : null;
    });

    return this.productRepo.save(_products);
  }

  async createNote(content: string) {
    const _note = new InventoryNote();
    _note.content = content;
    return this.noteRepo.save(_note);
  }

  async getNote() {
    const notes = await this.noteRepo.find();
    if (notes.length < 1) return this.createNote('');
    return notes[0];
  }

  async updateNote(content: string) {
    const note = await this.getNote();
    note.content = content;
    return this.noteRepo.save(note);
  }
}
