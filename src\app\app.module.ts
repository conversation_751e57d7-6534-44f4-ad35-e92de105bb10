import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from 'src/user/user.module';
import { AuthModule } from 'src/auth/auth.module';
import { StaffModule } from 'src/staff/staff.module';
import { TaskModule } from 'src/task/task.module';
import { WorkModule } from 'src/work/work.module';
import { InventoryModule } from 'src/inventory/inventory.module';
import { NewModule } from 'src/new/new.module';

@Module({
  imports: [
    UserModule,
    AuthModule,
    StaffModule,
    TaskModule,
    WorkModule,
    InventoryModule,
    NewModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
