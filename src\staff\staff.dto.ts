import { Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class StaffCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsNumber()
  staffTypeId: number;
}

export class StaffUpdateDto extends StaffCreateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class StaffIdDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class StaffParamDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  id: number;
}

export class StaffInfoDto {
  @IsOptional()
  @IsNumber()
  dateOffset?: number;
}

export class StaffStatsDto {
  @IsOptional()
  @IsNumber()
  fromDateOffset?: number;

  @IsOptional()
  @IsNumber()
  toDateOffset?: number;
}

export class StaffSortDto {
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  orders?: number[];
}

export class StaffAutoSortDto {
  @IsOptional()
  @IsNumber()
  dateOffset?: number;
}

export class StaffTypeCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

export class StaffTypeUpdateDto extends StaffTypeCreateDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class StaffTypeDeleteDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class StaffSaveWorkDto {
  @IsOptional()
  @IsNumber()
  dateOffset?: number;
}

export class StaffBusyHistoryDto {
  @IsOptional()
  @IsNumber()
  dateOffset?: number;

  @IsOptional()
  @IsNumber()
  staffId: number;
}
