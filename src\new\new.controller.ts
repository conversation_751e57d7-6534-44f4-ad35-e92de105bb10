import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { NewService } from './new.service';

@UseGuards(AuthGuard)
@Controller('new')
export class NewController {
  constructor(private readonly newService: NewService) {}

  @Get('stats')
  getAllStaffWork(@Query() query: any) {
    return this.newService.getAllStaffWork(query.fromDateOffset, query.toDateOffset);
  }

  @Get('group')
  getGroupStaffWorkByStaffType(@Query() query: any) {
    return this.newService.getGroupStaffWorkByStaffType(query.fromDateOffset, query.toDateOffset);
  }

  @Get('split')
  getGroupStaffWorkByDate(@Query() query: any) {
    return this.newService.getGroupStaffWorkByDate(
      query.fromDateOffset,
      query.toDateOffset,
      query.groupBy,
      query.staffTypeId,
      query.page,
      query.limit,
    );
  }
}
