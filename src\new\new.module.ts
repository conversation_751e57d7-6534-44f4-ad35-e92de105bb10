import { DatabaseModule } from 'src/database/database.module';
import { Modu<PERSON> } from '@nestjs/common';
import { NewController } from './new.controller';
import { NewService } from './new.service';
import { newProviders } from './new.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...newProviders, NewService],
  controllers: [NewController],
  exports: [NewService],
})
export class NewModule {}
