import { Inject, Injectable } from '@nestjs/common';
import { constants } from 'src/constants';

import { Repository } from 'typeorm';
import { TaskType } from './task-type.entity';
import { Task } from './task.entity';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class TaskService {
  constructor(
    @Inject(constants.db.TASK_TYPE_REPOSITORY)
    private taskTypeRepo: Repository<TaskType>,
    @Inject(constants.db.TASK_REPOSITORY)
    private taskRepo: Repository<Task>,
  ) {}

  async findAll(): Promise<Task[]> {
    return this.taskRepo.find({
      where: { isDelete: false },
      relations: ['taskType'],
      order: { orderIndex: 'ASC' },
    });
  }

  @Transactional()
  async save(data: any): Promise<Task> {
    return this.taskRepo.save(data);
  }

  @Transactional()
  async love(id: number): Promise<Task> {
    const task = await this.taskRepo.findOne({ where: { id } });
    return this.taskRepo.save({ id, isLove: !task.isLove });
  }

  async findAllType(): Promise<TaskType[]> {
    return this.taskTypeRepo.find({ where: { isDelete: false } });
  }

  @Transactional()
  async saveType(data: any): Promise<TaskType> {
    return this.taskTypeRepo.save(data);
  }

  @Transactional()
  async sort(orders: number[]) {
    const _tasks = await this.taskRepo.find({
      where: { isDelete: false },
    });

    _tasks.forEach((el) => {
      const _index = orders.findIndex((order) => order == el.id);
      el.orderIndex = _index >= 0 ? _index : null;
    });

    return this.taskRepo.save(_tasks);
  }
}
