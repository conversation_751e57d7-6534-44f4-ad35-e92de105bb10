import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { AuthGuard } from 'src/auth/auth.guard';
import {
  AddNoteDto,
  ChangeSettingDto,
  GetNoteDto,
  SettingInfoDto,
} from './user.dto';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(AuthGuard)
  @Get('/setting')
  getSetting(@Query() query: SettingInfoDto) {
    return this.userService.getSetting(query.dateOffset);
  }

  @UseGuards(AuthGuard)
  @Post('/change-setting')
  changeSetting(@Body() changeSettingDto: ChangeSettingDto) {
    const { applyRuleStaffType, ..._data } = changeSettingDto;

    return this.userService.changeSetting(_data, applyRuleStaffType);
  }

  @UseGuards(AuthGuard)
  @Post('/add-note')
  addNote(@Body() addNoteDto: AddNoteDto) {
    return this.userService.addNote(addNoteDto.content);
  }

  @UseGuards(AuthGuard)
  @Get('/get-note')
  getNote(@Query() query: GetNoteDto) {
    return this.userService.getNote(query.dateOffset);
  }
}
