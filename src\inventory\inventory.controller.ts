import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { InventoryService } from './inventory.service';
import {
  CreateProductDto,
  ProductSortDto,
  ProductTypeCreateDto,
  ProductTypeDeleteDto,
  ProductTypeUpdateDto,
  TransactionCreateDto,
  TransactionGroupByTransactionTypeDto,
  TransactionGroupDto,
  TransactionHistoryDto,
  UpdateProductDto,
} from './inventory.dto';

@UseGuards(AuthGuard)
@Controller('inventory')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Get('product')
  getAllProduct() {
    return this.inventoryService.findAllProduct();
  }

  @Get('product/warning')
  getWarningProduct() {
    return this.inventoryService.findWarningProduct();
  }

  @Get('product/request')
  getProductRequest() {
    return this.inventoryService.findWarningProduct();
  }

  @Post('product/delete')
  deleteProduct(@Body() body: any) {
    return this.inventoryService.deleteProduct(body.id);
  }

  @Post('product/create')
  createProduct(@Body() productDto: CreateProductDto) {
    return this.inventoryService.createProduct(
      productDto.name,
      productDto.unit,
      productDto.warningQuantity,
      productDto.qualifiedQuantity,
      productDto.productTypeId,
    );
  }

  @Post('product/update')
  updateProduct(@Body() productDto: UpdateProductDto) {
    return this.inventoryService.updateProduct(
      productDto.id,
      productDto.name,
      productDto.unit,
      productDto.warningQuantity,
      productDto.qualifiedQuantity,
    );
  }

  @Post('transaction/create')
  createTransaction(@Body() transactionDto: TransactionCreateDto) {
    return this.inventoryService.createTransaction(
      transactionDto.transactionType,
      transactionDto.details,
      transactionDto.note,
      transactionDto.staffId,
    );
  }

  @Get('transaction')
  getHistory(@Query() query: TransactionHistoryDto) {
    const _toDateOffset = isNaN(query.toDateOffset) ? query.fromDateOffset : query.toDateOffset;

    return this.inventoryService.getTransactionHistory(
      query.productId,
      query.fromDateOffset,
      _toDateOffset,
      query.transactionType,
      query.staffName,
      query.page,
      query.limit,
    );
  }

  @Get('transaction/detail/:id')
  getTransactionDetail(@Param() params: any) {
    return this.inventoryService.getTransactionDetail(params.id);
  }

  @Get('transaction/staff')
  getTransactionGroupByStaff(@Query() query: TransactionHistoryDto) {
    const _toDateOffset = isNaN(query.toDateOffset) ? query.fromDateOffset : query.toDateOffset;

    return this.inventoryService.getTransactionGroupByStaff(
      query.productId,
      query.fromDateOffset,
      _toDateOffset,
      query.transactionType,
      query.staffName,
      query.page,
      query.limit,
      query.asc,
    );
  }

  @Get('transaction/general')
  getTransactionGroupByTransactionType(@Query() query: TransactionGroupByTransactionTypeDto) {
    const _toDateOffset = isNaN(query.toDateOffset) ? query.fromDateOffset : query.toDateOffset;
    return this.inventoryService.getTransactionGroupByTransactionType(
      query.productId,
      query.fromDateOffset,
      _toDateOffset,
    );
  }

  @Get('transaction/group')
  getTransactionGroup(@Query() query: TransactionGroupDto) {
    const _toDateOffset = isNaN(query.toDateOffset) ? query.fromDateOffset : query.toDateOffset;
    return this.inventoryService.getTransactionGroup(
      query.productId,
      query.fromDateOffset,
      _toDateOffset,
      query.groupBy,
      query.page,
      query.limit,
    );
  }

  @Get('transaction/request')
  getTransactionRequest() {
    return this.inventoryService.getTransactionRequest();
  }

  @Get('product/type')
  getAllType() {
    return this.inventoryService.findAllType();
  }

  @Post('product/type/create')
  createType(@Body() dto: ProductTypeCreateDto) {
    return this.inventoryService.saveType(dto);
  }

  @Post('product/type/update')
  updateType(@Body() dto: ProductTypeUpdateDto) {
    return this.inventoryService.saveType(dto);
  }

  @Post('product/type/delete')
  deleteType(@Body() dto: ProductTypeDeleteDto) {
    return this.inventoryService.saveType({ ...dto, deletedAt: new Date() });
  }

  @Post('sort')
  sort(@Body() dto: ProductSortDto) {
    return this.inventoryService.sort(dto.orders);
  }

  @Get('note')
  getNote() {
    return this.inventoryService.getNote();
  }

  @Post('note/update')
  updateNote(@Body() dto: any) {
    return this.inventoryService.updateNote(dto.content);
  }
}
