import { DataSource } from 'typeorm';
import { constants } from '../constants';
import { Task } from './task.entity';
import { TaskType } from './task-type.entity';

export const taskProviders = [
  {
    provide: constants.db.TASK_TYPE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(TaskType),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.TASK_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Task),
    inject: [constants.db.DATA_SOURCE],
  },
];
