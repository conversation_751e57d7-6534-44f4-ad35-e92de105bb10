import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { StaffService } from './staff.service';
import {
  StaffAutoSortDto,
  StaffBusyHistoryDto,
  StaffCreateDto,
  StaffIdDto,
  StaffInfoDto,
  StaffParamDto,
  StaffSaveWorkDto,
  StaffSortDto,
  StaffStatsDto,
  StaffTypeCreateDto,
  StaffTypeDeleteDto,
  StaffTypeUpdateDto,
  StaffUpdateDto,
} from './staff.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { subDays } from 'date-fns';

@UseGuards(AuthGuard)
@Controller('staff')
export class StaffController {
  constructor(private readonly staffService: StaffService) {}

  @Get()
  getAll() {
    return this.staffService.findAll();
  }

  @Get('info')
  getInfo(@Query() query: StaffInfoDto) {
    return this.staffService.findAllWithInfo(query.dateOffset);
  }

  @Get('stats')
  stats(@Query() query: StaffStatsDto) {
    return this.staffService.findAllWithInfoRange(
      query.fromDateOffset,
      query.toDateOffset,
    );
  }

  @Get('info/:id')
  getOne(@Param() params: StaffParamDto) {
    return this.staffService.findOneWithInfo(params.id);
  }

  @Post('sort')
  sort(@Body() staffDto: StaffSortDto) {
    return this.staffService.saveOrderByUser(staffDto.orders);
  }

  @Post('sort/auto')
  autoSort(@Body() staffDto: StaffAutoSortDto) {
    return this.staffService.autoSort(staffDto.dateOffset);
  }

  @Post('leave')
  leave(@Body() staffDto: StaffIdDto) {
    return this.staffService.saveStatusStaffWork(staffDto.id, true);
  }

  @Post('comeback')
  comeback(@Body() staffDto: StaffIdDto) {
    return this.staffService.saveStatusStaffWork(staffDto.id);
  }

  @Post('busy')
  busy(@Body() staffDto: StaffIdDto) {
    return this.staffService.saveStatusStaffWork(staffDto.id, false, true);
  }

  @Post('remove-busy')
  removeBusy(@Body() staffDto: StaffIdDto) {
    return this.staffService.saveStatusStaffWork(staffDto.id);
  }

  @Post('create')
  create(@Body() staffDto: StaffCreateDto) {
    return this.staffService.save(staffDto);
  }

  @Post('update')
  update(@Body() staffDto: StaffUpdateDto) {
    return this.staffService.save(staffDto);
  }

  @Post('delete')
  delete(@Body() staffDto: StaffIdDto) {
    return this.staffService.save({ ...staffDto, isDelete: true });
  }

  @Get('type')
  getAllType() {
    return this.staffService.findAllType();
  }

  @Post('type/create')
  createType(@Body() taskTypeDto: StaffTypeCreateDto) {
    return this.staffService.saveType(taskTypeDto);
  }

  @Post('type/update')
  updateType(@Body() taskTypeDto: StaffTypeUpdateDto) {
    return this.staffService.saveType(taskTypeDto);
  }

  @Post('type/delete')
  deleteType(@Body() taskTypeDto: StaffTypeDeleteDto) {
    return this.staffService.saveType({ ...taskTypeDto, isDelete: true });
  }

  @Post('save-work')
  saveOlderWork(@Body() staffDto: StaffSaveWorkDto) {
    const _date = subDays(new Date(), staffDto.dateOffset);
    return this.staffService.saveOlderWork(_date);
  }

  @Get('busy/history')
  getBusyHistory(@Query() query: StaffBusyHistoryDto) {
    return this.staffService.findBusyHistory(query.staffId, query.dateOffset);
  }
}
