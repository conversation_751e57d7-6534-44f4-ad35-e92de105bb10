import { DataSource } from 'typeorm';
import { constants } from '../constants';
import { Staff } from './staff.entity';
import { StaffWork } from './staff-work.entity';
import { StaffOrder } from './staff-order.entity';
import { StaffType } from './staff-type.entity';
import { BusyHistory } from './busy-history.entity';

export const staffProviders = [
  {
    provide: constants.db.STAFF_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Staff),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.STAFF_WORK_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(StaffWork),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.STAFF_ORDER_REPOSITORY,
    useFactory: (dataSource: DataSource) =>
      dataSource.getRepository(StaffOrder),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.STAFF_TYPE_REPOSITORY,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(StaffType),
    inject: [constants.db.DATA_SOURCE],
  },
  {
    provide: constants.db.BUSY_HISTORY_REPOSITORY,
    useFactory: (dataSource: DataSource) =>
      dataSource.getRepository(BusyHistory),
    inject: [constants.db.DATA_SOURCE],
  },
];
